#!/bin/bash

# 待办事项管理器 - macOS应用卸载脚本
# 使用方法: ./uninstall_app.sh

APP_NAME="todo_app"
APP_FILE="${APP_NAME}.app"
INSTALL_PATH="/Applications/${APP_FILE}"

echo "🗑️  开始卸载待办事项管理器..."

# 检查应用是否已安装
if [ ! -d "$INSTALL_PATH" ]; then
    echo "⚠️  应用未安装或已被删除: $INSTALL_PATH"
    exit 1
fi

# 确认卸载
echo "📱 找到已安装的应用: $INSTALL_PATH"
APP_SIZE=$(du -sh "$INSTALL_PATH" | cut -f1)
echo "   应用大小: $APP_SIZE"
echo ""
read -p "确定要卸载待办事项管理器吗? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 卸载已取消"
    exit 1
fi

# 检查是否有管理员权限
if [ ! -w "/Applications" ]; then
    echo "⚠️  需要管理员权限来卸载应用"
    echo "请输入密码以继续卸载..."
fi

# 删除应用
echo "🗑️  正在删除应用..."
sudo rm -rf "$INSTALL_PATH"

# 验证卸载
if [ ! -d "$INSTALL_PATH" ]; then
    echo "✅ 卸载成功!"
    echo "📱 应用已从系统中完全删除"
    echo ""
    echo "💡 注意:"
    echo "- 应用的用户数据(待办事项)仍保存在系统中"
    echo "- 如需完全清除数据，请手动删除相关偏好设置文件"
    echo ""
    
    # 刷新启动台缓存
    echo "🔄 刷新启动台缓存..."
    sudo killall Dock
    
    echo "🎉 卸载完成! 请稍等片刻让启动台更新..."
else
    echo "❌ 卸载失败，请检查错误信息"
    exit 1
fi

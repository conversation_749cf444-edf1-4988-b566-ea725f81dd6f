#!/bin/bash

# 待办事项管理器 - macOS应用安装脚本
# 使用方法: ./install_app.sh

APP_NAME="todo_app"
APP_FILE="${APP_NAME}.app"
BUILD_PATH="build/macos/Build/Products/Release/${APP_FILE}"
INSTALL_PATH="/Applications/${APP_FILE}"

echo "🚀 开始安装待办事项管理器到系统应用程序文件夹..."

# 检查应用文件是否存在
if [ ! -d "$BUILD_PATH" ]; then
    echo "❌ 错误: 找不到应用文件 $BUILD_PATH"
    echo "请先运行 ./build_app.sh 构建应用"
    exit 1
fi

# 检查是否有管理员权限
if [ ! -w "/Applications" ]; then
    echo "⚠️  需要管理员权限来安装应用到 /Applications/ 目录"
    echo "请输入密码以继续安装..."
fi

# 如果应用已存在，询问是否覆盖
if [ -d "$INSTALL_PATH" ]; then
    echo "⚠️  应用已存在: $INSTALL_PATH"
    read -p "是否要覆盖现有应用? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 安装已取消"
        exit 1
    fi
    echo "🗑️  删除现有应用..."
    sudo rm -rf "$INSTALL_PATH"
fi

# 复制应用到Applications目录
echo "📦 复制应用到 /Applications/ 目录..."
sudo cp -R "$BUILD_PATH" "/Applications/"

# 设置正确的权限
echo "🔐 设置应用权限..."
sudo chown -R root:admin "$INSTALL_PATH"
sudo chmod -R 755 "$INSTALL_PATH"

# 验证安装
if [ -d "$INSTALL_PATH" ]; then
    echo "✅ 安装成功!"
    echo "📱 应用已安装到: $INSTALL_PATH"
    echo ""
    echo "🎉 现在你可以:"
    echo "1. 从启动台找到并启动'todo_app'"
    echo "2. 从应用程序文件夹启动应用"
    echo "3. 使用Spotlight搜索'todo_app'"
    echo ""
    echo "📝 应用信息:"
    APP_SIZE=$(du -sh "$INSTALL_PATH" | cut -f1)
    echo "   应用大小: $APP_SIZE"
    echo "   安装位置: $INSTALL_PATH"
    echo "   版本: 2.0.0"
    echo ""
    
    # 刷新启动台缓存
    echo "🔄 刷新启动台缓存..."
    sudo killall Dock
    
    echo "🚀 安装完成! 请稍等片刻让启动台更新..."
else
    echo "❌ 安装失败，请检查错误信息"
    exit 1
fi

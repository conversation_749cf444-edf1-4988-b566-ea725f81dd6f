# todo_app

A new Flutter project.

## Getting Started

# 待办事项管理器 📝

一个使用 Dart 和 Flutter 开发的简单桌面待办事项管理应用，专为 macOS 设计。

## 功能特性 ✨

### 核心功能
- ✏️ **智能任务管理** - 添加、编辑、删除待办事项，支持标题和详细描述
- ✅ **状态跟踪** - 点击复选框标记任务完成状态
- 📅 **截止日期** - 设置任务截止日期，自动过期提醒
- 🏷️ **分类管理** - 5个预设分类（工作、学习、生活、健康、默认）
- 🔍 **智能搜索** - 按标题和描述搜索，按分类过滤
- 📝 **任务描述** - 支持最多200字符的详细任务描述
- 💾 **数据持久化** - 所有数据自动保存到本地

### 高级功能
- 📊 **统计分析** - 完成率、分类统计、进度可视化
- ⚠️ **智能提醒** - 过期任务红色标识，即将到期橙色提醒
- 🎯 **智能排序** - 按优先级自动排序（过期→即将到期→截止日期→创建时间）
- 🎨 **现代界面** - Material Design 3 + 自定义美化设计
- 📱 **多页面导航** - 任务列表、统计信息、设置页面
- 🌙 **深色模式** - 完整的主题切换系统，支持浅色/深色模式
- ✨ **精美UI设计** - 渐变背景、圆角卡片、阴影效果、动画交互
- 🛠️ **设置管理** - 数据清除、应用信息、使用统计、主题切换

## 技术栈 🛠️

- **语言**: Dart 3.8.1
- **框架**: Flutter 3.32.8
- **平台**: macOS 桌面应用
- **数据存储**: SharedPreferences (本地存储)
- **UI设计**: Material Design 3
- **架构模式**: StatefulWidget + 状态管理
- **UI设计**: 自定义组件 + 渐变效果 + 动画交互
- **代码规模**: 1445行高质量Dart代码

## 快速开始 🚀

### 前置要求

- macOS 系统
- Flutter SDK (3.32.8 或更高版本)
- Xcode (用于 macOS 开发)

### 运行应用

1. **开发模式运行**:
   ```bash
   flutter run -d macos
   ```

2. **构建发布版本**:
   ```bash
   ./build_app.sh
   ```

3. **安装到系统**:
   ```bash
   ./install_app.sh
   ```
   安装后可以从启动台、应用程序文件夹或Spotlight搜索启动

4. **卸载应用**:
   ```bash
   ./uninstall_app.sh
   ```

### 项目结构

```
todo_app/
├── lib/
│   └── main.dart          # 主应用代码 (1445行)
├── macos/                 # macOS 平台特定代码
├── build_app.sh          # 构建脚本
├── install_app.sh        # 安装脚本
├── uninstall_app.sh      # 卸载脚本
├── README.md             # 项目说明
└── build/                 # 构建产物
    └── macos/Build/Products/Release/
        └── todo_app.app   # 可执行应用 (51.7MB)
```

## 学习要点 📚

这个项目是学习 Dart 和 Flutter 的绝佳示例，涵盖了：

### Dart 语言特性
- 类和对象的定义
- 构造函数和工厂方法
- JSON 序列化和反序列化
- 异步编程 (async/await)
- 泛型和集合操作

### Flutter 框架概念
- StatefulWidget 和 StatelessWidget
- 状态管理 (setState)
- Widget 组合和布局
- Material Design 组件
- 生命周期方法

### 桌面应用开发
- macOS 平台配置
- 本地数据持久化
- 用户界面设计
- 应用构建和打包

## 代码亮点 💡

### 增强的数据模型
```dart
class TodoItem {
  String id;
  String title;
  bool isCompleted;
  DateTime createdAt;
  DateTime? dueDate;     // 截止日期
  String category;       // 分类
  List<String> tags;     // 标签

  // 智能状态检测
  bool get isOverdue { ... }
  bool get isDueSoon { ... }

  // JSON 序列化支持
  Map<String, dynamic> toJson() { ... }
  factory TodoItem.fromJson(Map<String, dynamic> json) { ... }
}
```

### 智能过滤和排序
```dart
List<TodoItem> get _filteredTodos {
  List<TodoItem> filtered = _todos;

  // 搜索过滤
  if (_searchQuery.isNotEmpty) {
    filtered = filtered.where((todo) =>
        todo.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
  }

  // 分类过滤
  if (_filterCategory != '全部') {
    filtered = filtered.where((todo) => todo.category == _filterCategory).toList();
  }

  // 智能排序：过期 → 即将到期 → 截止日期 → 创建时间
  filtered.sort((a, b) { ... });

  return filtered;
}
```

### 多页面架构
```dart
Widget build(BuildContext context) {
  return Scaffold(
    body: IndexedStack(
      index: _currentIndex,
      children: [
        _buildTodoListPage(),    // 任务列表
        _buildStatisticsPage(),  // 统计分析
        _buildSettingsPage(),    // 设置页面
      ],
    ),
    bottomNavigationBar: BottomNavigationBar(...),
  );
}
```

### 深色模式主题系统
```dart
class _TodoAppState extends State<TodoApp> {
  bool _isDarkMode = false;
  static const String _themeKey = 'isDarkMode';

  // 主题切换
  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    _saveThemePreference(_isDarkMode);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
        scaffoldBackgroundColor: const Color(0xFF121212),
      ),
      themeMode: _isDarkMode ? ThemeMode.dark : ThemeMode.light,
    );
  }
}
```

## 已实现的高级功能 🎉

✅ **截止日期管理** - 完整的日期选择和过期提醒系统
✅ **任务分类和标签** - 5个预设分类，颜色标识系统
✅ **搜索和过滤功能** - 支持标题搜索和分类过滤
✅ **统计和进度显示** - 完整的数据可视化和进度跟踪
✅ **多页面导航** - 现代化的应用架构
✅ **深色模式支持** - 完整的主题切换系统，支持主题偏好持久化
✅ **精美UI设计** - 渐变背景、圆角卡片、阴影效果、动画交互

## 未来扩展建议 🔮

可以考虑添加以下功能来进一步学习：
- ☁️ **云端同步功能** - Firebase或其他云服务集成
- 🔔 **系统通知** - macOS原生通知提醒
- 📱 **跨平台支持** - iOS、Windows、Linux版本
- 🎨 **自定义主题** - 用户自定义颜色和字体
- 📊 **高级图表** - 更丰富的数据可视化
- 🔄 **数据导入导出** - CSV、JSON格式支持
- 👥 **多用户支持** - 用户账户和数据隔离

## 许可证 📄

本项目仅用于学习目的。

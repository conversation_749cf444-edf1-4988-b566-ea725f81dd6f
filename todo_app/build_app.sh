#!/bin/bash

# 待办事项管理器 - macOS桌面应用构建脚本
# 使用方法: ./build_app.sh

echo "🚀 开始构建待办事项管理器桌面应用..."

# 检查Flutter是否安装
if ! command -v flutter &> /dev/null; then
    echo "❌ 错误: Flutter未安装或未添加到PATH"
    echo "请先安装Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# 检查是否启用了macOS桌面支持
echo "📋 检查Flutter配置..."
flutter config --enable-macos-desktop

# 获取依赖
echo "📦 获取项目依赖..."
flutter pub get

# 构建Release版本
echo "🔨 构建Release版本..."
flutter build macos --release

# 检查构建结果
if [ -d "build/macos/Build/Products/Release/todo_app.app" ]; then
    echo "✅ 构建成功!"
    echo "📱 应用位置: build/macos/Build/Products/Release/todo_app.app"
    echo ""
    echo "🎉 你可以通过以下方式运行应用:"
    echo "1. 在Finder中打开 build/macos/Build/Products/Release/ 目录"
    echo "2. 双击 todo_app.app 运行应用"
    echo "3. 或者将应用拖拽到应用程序文件夹"
    echo ""
    echo "📝 应用功能:"
    echo "- ✏️  添加待办事项"
    echo "- ✅ 标记完成状态"
    echo "- 🗑️  删除待办事项"
    echo "- 💾 自动保存到本地"
    echo ""
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi
